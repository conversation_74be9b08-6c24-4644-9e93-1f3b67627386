@echo off
echo ================================================================
echo    Adaptive Meeting Twin - Integrated AI System Launcher
echo ================================================================
echo.
echo Features:
echo  ✓ Enhanced Signaling Server with AI APIs
echo  ✓ Gemini AI Meeting Summaries
echo  ✓ VADER Sentiment Analysis with Smooth Graphs
echo  ✓ Real-time Attention Tracking
echo  ✓ WebRTC Video Conferencing
echo.
echo ================================================================

REM Check if we're in the correct directory
if not exist "working_server.py" (
    echo ERROR: working_server.py not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

if not exist "frontend\index.html" (
    echo ERROR: frontend\index.html not found!
    echo Please ensure the frontend directory exists.
    pause
    exit /b 1
)

echo [1/4] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again.
    pause
    exit /b 1
)

echo [2/4] Checking required Python packages...
python -c "import flask, flask_socketio, google.generativeai, nltk, cv2, mediapipe" >nul 2>&1
if errorlevel 1 (
    echo WARNING: Some required packages may be missing.
    echo Installing required packages...
    pip install flask flask-socketio google-generativeai nltk scipy opencv-python mediapipe numpy
    if errorlevel 1 (
        echo ERROR: Failed to install required packages.
        echo Please install manually: pip install flask flask-socketio google-generativeai nltk scipy opencv-python mediapipe numpy
        pause
        exit /b 1
    )
)

echo [3/4] Starting Enhanced Backend Server...
echo.
echo Backend will be available at: http://localhost:6000
echo API Endpoints:
echo   - POST /api/summarize     (AI Meeting Summary)
echo   - POST /api/sentiment     (Advanced Sentiment Analysis)
echo   - POST /api/attention/*   (Attention Tracking)
echo.

REM Start the backend server in a new window
start "Adaptive Meeting Twin - Backend" cmd /k "python working_server.py"

REM Wait a moment for the server to start
timeout /t 3 /nobreak >nul

echo [4/4] Opening Frontend...
echo.
echo Frontend will open in your default browser.
echo If it doesn't open automatically, navigate to:
echo   file:///%CD%\frontend\index.html
echo.

REM Open the frontend in the default browser
start "" "frontend\index.html"

echo ================================================================
echo                    System Started Successfully!
echo ================================================================
echo.
echo Instructions:
echo 1. The backend server is running in a separate window
echo 2. The frontend is now open in your browser
echo 3. Enter a room name and click "Join Room" to start
echo 4. Enable transcription to use AI features
echo 5. Click "Generate Summary" for AI-powered meeting minutes
echo 6. Use "Start Attention Tracking" for real-time attention monitoring
echo.
echo To stop the system:
echo - Close the backend server window
echo - Close the browser tab
echo.
echo Troubleshooting:
echo - If the backend fails to start, check the server window for errors
echo - Ensure your camera/microphone permissions are granted
echo - For attention tracking, make sure your camera is working
echo.
echo ================================================================

pause
