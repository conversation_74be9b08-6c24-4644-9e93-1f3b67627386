import nltk
from nltk.sentiment.vader import SentimentIntensityAnalyzer
import matplotlib.pyplot as plt
import numpy as np
from scipy.interpolate import make_interp_spline

# Ensure downloads (only needed once)
nltk.download('vader_lexicon')
nltk.download('punkt')

def plot_smooth_sentiment(paragraph, window=5):
    # Tokenize words
    words = nltk.word_tokenize(paragraph)
    sia = SentimentIntensityAnalyzer()

    # Sliding window sentiment scores
    scores = []
    for i in range(0, len(words), window):
        chunk = " ".join(words[i:i+window])
        score = sia.polarity_scores(chunk)['compound']
        scores.append(score)

    x = np.arange(len(scores))
    y = np.array(scores)

    # Interpolation for smooth curve
    if len(x) > 3:  # spline requires >= 4 points
        X_ = np.linspace(x.min(), x.max(), 300)
        spl = make_interp_spline(x, y, k=3)
        Y_ = spl(X_)
    else:
        X_, Y_ = x, y

    # ---- Stylish Plot ----
    plt.style.use("seaborn-v0_8-darkgrid")
    fig, ax = plt.subplots(figsize=(12, 6))

    # Smooth curve
    ax.plot(X_, Y_, color="royalblue", linewidth=2.5, alpha=0.85, label="Sentiment Trend")
    # Actual points
    ax.scatter(x, y, color="orange", s=100, edgecolors="black", zorder=5)

    # Thresholds + background zones
    ax.axhline(0.05, color="green", linestyle="--", linewidth=1.2, label="Positive threshold")
    ax.axhline(-0.05, color="red", linestyle="--", linewidth=1.2, label="Negative threshold")
    ax.axhspan(0.05, 1, facecolor="palegreen", alpha=0.2)
    ax.axhspan(-1, -0.05, facecolor="mistyrose", alpha=0.2)

    # Labels and styling
    ax.set_title("🌈 Smooth Sentiment Graph (Word-level)", fontsize=18, fontweight="bold", color="navy", pad=20)
    ax.set_xlabel(f"Chunk Index ({window} words per chunk)", fontsize=14)
    ax.set_ylabel("Sentiment Score", fontsize=14)
    ax.set_ylim(-1, 1)
    ax.legend(fontsize=12)
    plt.tight_layout()
    plt.show()

    # Print chunk results in console
    print("\n--- Sentiment per chunk ---")
    for i, (sc, chunk) in enumerate(zip(scores, [words[j:j+window] for j in range(0, len(words), window)]), 1):
        label = "Positive" if sc >= 0.05 else "Negative" if sc <= -0.05 else "Neutral"
        print(f"{i}. {label.upper()} ({sc:.2f}) - {' '.join(chunk)}")


# -------- Run from Terminal --------
if __name__ == "__main__":
    print("👉 Paste your paragraph for sentiment analysis:\n")
    paragraph = input()
    plot_smooth_sentiment(paragraph, window=5)
