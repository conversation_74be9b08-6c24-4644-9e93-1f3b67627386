#!/usr/bin/env python3
print("Test server starting...")

try:
    from flask import Flask
    print("Flask imported successfully")
    
    from flask_socketio import SocketIO
    print("SocketIO imported successfully")
    
    app = Flask(__name__)
    print("Flask app created")
    
    socketio = SocketIO(app, cors_allowed_origins="*")
    print("SocketIO initialized")
    
    @app.route("/")
    def home():
        return "Test server is running"
    
    print("Route defined")
    
    if __name__ == "__main__":
        print("About to start server on port 6000...")
        socketio.run(app, host="0.0.0.0", port=6000, debug=True, allow_unsafe_werkzeug=True)
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
