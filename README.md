# Adaptive Meeting Twin

A fully functional WebRTC-based video conferencing application with adaptive fallback, real-time transcription, engagement tracking, sentiment analysis, and meeting recording capabilities.

## Features

### Core WebRTC Functionality
- **Automatic Room Joining**: No manual SDP copy/paste required
- **Peer-to-Peer Video/Audio**: Direct connection between participants
- **ICE Candidate Exchange**: Automatic NAT traversal
- **Connection State Monitoring**: Real-time connection status

### Adaptive Network Fallback
- **Normal Mode**: Full HD video and audio
- **Degraded Mode**: Reduced video quality (640x480, 15fps)
- **Audio-Only Mode**: Video disabled, audio continues
- **Transcript-Only Mode**: All media disabled, text-only communication

### Advanced Features
- **Live Transcription**: Real-time speech-to-text using Web Speech API
- **Engagement Tracking**: Speaking time analysis and participant metrics
- **Sentiment Analysis**: Real-time mood analysis of conversation
- **Minutes of Meeting (MOM)**: Automatic meeting summarization
- **Action Items**: Extraction of tasks and assignments
- **Meeting Recording**: Full audio/video recording with download

## Architecture

### Backend (Python Flask-SocketIO)
- **Signaling Server**: Handles WebRTC offer/answer exchange
- **Room Management**: Automatic peer discovery and joining
- **Real-time Communication**: WebSocket-based signaling

### Frontend (Vanilla JavaScript)
- **WebRTC Implementation**: Complete peer connection handling
- **Adaptive UI**: Dynamic quality adjustment based on network
- **Real-time Features**: Live transcription, engagement, sentiment
- **Recording System**: MediaRecorder API integration

## Installation & Setup

### Prerequisites
- Python 3.8+
- Modern web browser (Chrome, Firefox, Edge)
- Microphone and camera access

### Backend Setup
1. Install Python dependencies:
```bash
pip install flask flask-socketio
```

2. Start the signaling server:
```bash
python working_server.py
```
The server will run on `http://localhost:6000`

### Frontend Setup
1. Start the frontend server:
```bash
python simple_server.py
```
The frontend will be available at `http://localhost:8000`

### Quick Start (Windows)
1. Double-click `start_backend.bat` to start the signaling server
2. Double-click `start_frontend.bat` to start the frontend server
3. Open `http://localhost:8000` in your browser

## Usage

### Joining a Meeting
1. Open the application in your browser
2. Enter a room name (e.g., "room1")
3. Click "Join Room"
4. Allow camera/microphone access when prompted
5. Share the same room name with other participants

### Features Usage

#### Transcription
- Check "Enable mic transcription" to start real-time speech-to-text
- Transcripts appear in the Live Transcript panel
- Works automatically in Transcript-Only fallback mode

#### Engagement Tracking
- Speaking time is tracked automatically
- Visual bars show participation levels
- Meeting Champion and Silent Listener badges update in real-time

#### Sentiment Analysis
- Analyzes emotional tone of conversation
- Real-time sentiment chart updates during the meeting
- Positive/Negative/Neutral sentiment tracking

#### Meeting Recording
- Click "Start Recording" to begin capturing audio/video
- Click "Stop Recording" to end and generate downloadable file
- Playback available immediately after stopping

#### Minutes of Meeting
- Click "Summarize → MOM & Actions" to generate meeting summary
- Automatically extracts key topics and action items
- Based on complete transcript analysis

## Network Adaptation

The system automatically adapts to network conditions:

- **RTT > 500ms or Video Loss > 50**: Transcript-Only mode
- **RTT > 300ms or Video Loss > 20**: Audio-Only mode  
- **RTT > 150ms or Video Loss > 5**: Degraded video quality
- **Good conditions**: Full HD video and audio

## Browser Compatibility

### Fully Supported
- Chrome 80+
- Firefox 75+
- Edge 80+
- Safari 14+

### Required Permissions
- Camera access
- Microphone access
- Autoplay (for remote video)

## Troubleshooting

### Common Issues

**"Speech recognition not supported"**
- Use Chrome or Edge browser
- Ensure HTTPS or localhost connection

**"Error accessing camera/microphone"**
- Check browser permissions
- Ensure devices are not in use by other applications

**Connection fails**
- Check if signaling server is running on port 6000
- Verify firewall settings
- Try refreshing the page

**No remote video/audio**
- Both participants must be in the same room
- Check network connectivity
- Verify WebRTC is supported

### Debug Mode
- Open browser Developer Tools (F12)
- Check Console for error messages
- Monitor Network tab for WebSocket connections

## File Structure

```
├── backend/
│   └── server.py              # Original signaling server
├── frontend/
│   ├── index.html            # Main application UI
│   ├── app.js               # Complete WebRTC implementation
│   └── styles.css           # Application styling
├── working_server.py        # Enhanced signaling server
├── simple_server.py         # Frontend HTTP server
├── start_backend.bat        # Windows backend launcher
├── start_frontend.bat       # Windows frontend launcher
└── README.md               # This file
```

## Technical Details

### WebRTC Configuration
- STUN servers: Google public STUN servers
- ICE candidate gathering: Automatic
- Media constraints: Adaptive based on network conditions

### Signaling Protocol
- WebSocket-based using Socket.IO
- Room-based peer discovery
- Automatic offer/answer exchange
- ICE candidate relay

### Security Considerations
- CORS enabled for development
- No authentication (add as needed)
- Local network operation recommended

## License

This project is provided as-is for educational and development purposes.
