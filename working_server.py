#!/usr/bin/env python3
"""
Adaptive Meeting Twin - Enhanced Signaling Server
A Flask-SocketIO server for WebRTC signaling with AI features
"""

import sys
import os
import json
import threading
import time
from datetime import datetime

# Add some debug output
print("=" * 50)
print("Adaptive Meeting Twin - Enhanced Signaling Server")
print("=" * 50)
print(f"Python version: {sys.version}")
print(f"Working directory: {os.getcwd()}")

try:
    from flask import Flask, request, jsonify
    print("✓ Flask imported successfully")

    from flask_socketio import SocketIO, join_room, emit
    print("✓ Flask-SocketIO imported successfully")

    # AI and ML imports
    import google.generativeai as genai
    print("✓ Google Generative AI imported successfully")

    import nltk
    from nltk.sentiment.vader import SentimentIntensityAnalyzer
    print("✓ NLTK imported successfully")

    import numpy as np
    from scipy.interpolate import make_interp_spline
    print("✓ NumPy and SciPy imported successfully")

    # Computer vision imports for attention tracking
    import cv2
    import mediapipe as mp
    from collections import deque
    print("✓ OpenCV and MediaPipe imported successfully")

    # Import attention tracking service
    from attention_service import get_attention_tracker
    print("✓ Attention tracking service imported successfully")

except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Please install missing dependencies:")
    print("pip install google-generativeai nltk scipy opencv-python mediapipe")
    sys.exit(1)

# Initialize AI components
print("\n--- Initializing AI Components ---")

# Configure Gemini AI
API_KEY = "AIzaSyDD3-4VGqZbncObJ7_VFA2UeovjrEO6ag0"  # ⚠️ Demo key
genai.configure(api_key=API_KEY)
model = genai.GenerativeModel("gemini-1.5-flash")
print("✓ Gemini AI configured")

# Initialize NLTK components
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
    sia = SentimentIntensityAnalyzer()
    print("✓ NLTK sentiment analyzer ready")
except Exception as e:
    print(f"⚠️ NLTK setup warning: {e}")
    sia = None

# Initialize MediaPipe for attention tracking
mp_face_mesh = mp.solutions.face_mesh
mp_drawing = mp.solutions.drawing_utils
print("✓ MediaPipe face mesh initialized")

# Attention tracking configuration
CLOSE_EYE_THRESHOLD = 0.22
FPS = 30
LEFT_EYE_INDICES = [33, 160, 158, 133, 153, 144]
RIGHT_EYE_INDICES = [362, 385, 387, 263, 373, 380]
LEFT_IRIS = [468, 469, 470, 471]
RIGHT_IRIS = [473, 474, 475, 476]

# Global variables for attention tracking
attention_sessions = {}  # room_id -> attention_data
sentiment_sessions = {}  # room_id -> sentiment_data

print("✓ AI components initialized successfully")

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'adaptive-meeting-twin-secret'
print("✓ Flask app created")

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)
print("✓ SocketIO initialized with CORS enabled")

# AI Service Functions
def summarize_and_extract(transcript):
    """Generate AI summary using Gemini"""
    try:
        prompt = f"""
        You are an AI meeting assistant. Analyze the following transcript and produce:
        1. A concise meeting summary (3-5 sentences).
        2. Clear meeting minutes (MOM).
        3. Action items with assignees if mentioned.

        Transcript:
        {transcript}

        Format the response as:
        📋 Summary:
        ...

        📝 Meeting Minutes:
        - Point 1
        - Point 2

        ✅ Action Items:
        - Task 1
        - Task 2
        """

        response = model.generate_content(prompt)
        return {"success": True, "summary": response.text}
    except Exception as e:
        return {"success": False, "error": str(e)}

def analyze_sentiment_advanced(text, window=5):
    """Advanced sentiment analysis with smooth interpolation"""
    if not sia:
        return {"success": False, "error": "NLTK not available"}

    try:
        words = nltk.word_tokenize(text)
        scores = []
        chunks = []

        for i in range(0, len(words), window):
            chunk = " ".join(words[i:i+window])
            score = sia.polarity_scores(chunk)['compound']
            scores.append(score)
            chunks.append(chunk)

        # Create smooth interpolation if enough points
        if len(scores) > 3:
            x = np.arange(len(scores))
            y = np.array(scores)
            X_ = np.linspace(x.min(), x.max(), min(300, len(scores) * 10))
            spl = make_interp_spline(x, y, k=3)
            Y_ = spl(X_)
            smooth_data = {"x": X_.tolist(), "y": Y_.tolist()}
        else:
            smooth_data = {"x": list(range(len(scores))), "y": scores}

        return {
            "success": True,
            "scores": scores,
            "chunks": chunks,
            "smooth_data": smooth_data,
            "overall_sentiment": np.mean(scores) if scores else 0
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def eye_aspect_ratio(landmarks, indices, w, h):
    """Calculate eye aspect ratio for attention tracking"""
    pts = [(int(landmarks[i].x * w), int(landmarks[i].y * h)) for i in indices]
    A = np.linalg.norm(np.array(pts[1]) - np.array(pts[5]))
    B = np.linalg.norm(np.array(pts[2]) - np.array(pts[4]))
    C = np.linalg.norm(np.array(pts[0]) - np.array(pts[3]))
    ear = (A + B) / (2.0 * C)
    return ear

# Flask Routes
@app.route("/")
def home():
    return """
    <h1>Adaptive Meeting Twin - Enhanced Signaling Server</h1>
    <p>Server is running successfully with AI features!</p>
    <p>Frontend should connect to: <code>http://localhost:6000</code></p>
    <p>WebSocket endpoint: <code>ws://localhost:6000/socket.io/</code></p>
    <h2>Available AI Endpoints:</h2>
    <ul>
        <li><code>POST /api/summarize</code> - Generate AI meeting summary</li>
        <li><code>POST /api/sentiment</code> - Advanced sentiment analysis</li>
        <li><code>POST /api/attention/start</code> - Start attention tracking</li>
        <li><code>POST /api/attention/stop</code> - Stop attention tracking</li>
    </ul>
    """

@app.route("/status")
def status():
    return {"status": "running", "message": "Enhanced signaling server is operational", "ai_features": True}

@app.route("/api/summarize", methods=["POST"])
def api_summarize():
    """API endpoint for AI summary generation"""
    try:
        data = request.get_json()
        transcript = data.get("transcript", "")

        if not transcript:
            return jsonify({"success": False, "error": "No transcript provided"}), 400

        result = summarize_and_extract(transcript)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/sentiment", methods=["POST"])
def api_sentiment():
    """API endpoint for advanced sentiment analysis"""
    try:
        data = request.get_json()
        text = data.get("text", "")
        window = data.get("window", 5)

        if not text:
            return jsonify({"success": False, "error": "No text provided"}), 400

        result = analyze_sentiment_advanced(text, window)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/attention/start", methods=["POST"])
def api_attention_start():
    """API endpoint to start attention tracking for a room"""
    try:
        data = request.get_json()
        room = data.get("room", "default")

        if room not in attention_sessions:
            attention_sessions[room] = {
                "active": True,
                "scores": deque(maxlen=100),
                "timestamps": deque(maxlen=100),
                "ear_history": deque(maxlen=5),
                "att_history": deque(maxlen=10)
            }

        attention_sessions[room]["active"] = True
        return jsonify({"success": True, "message": f"Attention tracking started for room {room}"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/attention/stop", methods=["POST"])
def api_attention_stop():
    """API endpoint to stop attention tracking for a room"""
    try:
        data = request.get_json()
        room = data.get("room", "default")

        if room in attention_sessions:
            attention_sessions[room]["active"] = False

        # Stop the attention tracker
        tracker = get_attention_tracker()
        tracker.stop_tracking()

        return jsonify({"success": True, "message": f"Attention tracking stopped for room {room}"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/attention/analyze", methods=["POST"])
def api_attention_analyze():
    """API endpoint to analyze a frame for attention metrics"""
    try:
        data = request.get_json()
        base64_frame = data.get("frame", "")
        room = data.get("room", "default")

        if not base64_frame:
            return jsonify({"success": False, "error": "No frame provided"}), 400

        # Get attention tracker and analyze frame
        tracker = get_attention_tracker()
        if not tracker.is_tracking:
            tracker.start_tracking()

        result = tracker.process_base64_frame(base64_frame)

        if "error" in result:
            return jsonify({"success": False, "error": result["error"]}), 400

        # Store in room session if tracking is active
        if room in attention_sessions and attention_sessions[room]["active"]:
            attention_sessions[room]["scores"].append(result["attention_score"])
            attention_sessions[room]["timestamps"].append(result["timestamp"])

        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

# WebSocket Event Handlers
@socketio.on("connect")
def on_connect():
    print(f"Client connected: {request.sid}")
    emit("connected", {"message": "Connected to enhanced signaling server"})

@socketio.on("disconnect")
def on_disconnect():
    print(f"Client disconnected: {request.sid}")

@socketio.on("join")
def on_join(data):
    room = data.get("room")
    if not room:
        emit("error", {"message": "Room name is required"})
        return

    join_room(room)
    print(f"Client {request.sid} joined room: {room}")

    # Initialize room data if not exists
    if room not in sentiment_sessions:
        sentiment_sessions[room] = {"data": [], "active": True}
    if room not in attention_sessions:
        attention_sessions[room] = {
            "active": False,
            "scores": deque(maxlen=100),
            "timestamps": deque(maxlen=100),
            "ear_history": deque(maxlen=5),
            "att_history": deque(maxlen=10)
        }

    emit("joined", {"room": room, "id": request.sid})
    emit("peer-joined", {"id": request.sid}, room=room, include_self=False)

@socketio.on("signal")
def on_signal(data):
    room = data.get("room")
    signal_data = data.get("data")

    if not room or not signal_data:
        emit("error", {"message": "Room and signal data are required"})
        return

    print(f"Relaying signal from {request.sid} in room {room}")
    emit("signal", {"id": request.sid, "data": signal_data}, room=room, include_self=False)

@socketio.on("sentiment_analysis")
def on_sentiment_analysis(data):
    """Handle real-time sentiment analysis requests"""
    room = data.get("room")
    text = data.get("text", "")

    if not room or not text:
        emit("error", {"message": "Room and text are required"})
        return

    result = analyze_sentiment_advanced(text)
    if result["success"]:
        # Store in room session
        if room in sentiment_sessions:
            sentiment_sessions[room]["data"].append({
                "timestamp": time.time(),
                "text": text,
                "sentiment": result["overall_sentiment"],
                "detailed": result
            })

        # Broadcast to room
        emit("sentiment_result", result, room=room)
    else:
        emit("sentiment_error", result, room=room)

@socketio.on("attention_data")
def on_attention_data(data):
    """Handle attention tracking data from frontend"""
    room = data.get("room")
    attention_score = data.get("attention_score", 0)

    if not room:
        emit("error", {"message": "Room is required"})
        return

    if room in attention_sessions and attention_sessions[room]["active"]:
        current_time = time.time()
        attention_sessions[room]["scores"].append(attention_score)
        attention_sessions[room]["timestamps"].append(current_time)

        # Broadcast to room
        emit("attention_update", {
            "attention_score": attention_score,
            "timestamp": current_time
        }, room=room)

if __name__ == "__main__":
    print("\n" + "=" * 60)
    print("Starting Enhanced Adaptive Meeting Twin Server...")
    print("Server will be available at: http://localhost:6000")
    print("AI Features: ✓ Gemini Summary ✓ VADER Sentiment ✓ Attention Tracking")
    print("Press Ctrl+C to stop the server")
    print("=" * 60 + "\n")

    try:
        socketio.run(
            app,
            host="0.0.0.0",
            port=6000,
            debug=True,
            allow_unsafe_werkzeug=True,
            use_reloader=False  # Disable reloader to avoid issues
        )
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")
        import traceback
        traceback.print_exc()
