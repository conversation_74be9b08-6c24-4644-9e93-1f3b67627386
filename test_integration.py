#!/usr/bin/env python3
"""
Integration Test Script for Adaptive Meeting Twin
Tests all AI features and integrations
"""

import requests
import json
import time
import sys
import base64
import cv2
import numpy as np

def test_server_status():
    """Test if the server is running"""
    try:
        response = requests.get('http://localhost:6000/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✓ Server is running")
            print(f"  Status: {data.get('status')}")
            print(f"  AI Features: {data.get('ai_features')}")
            return True
        else:
            print(f"✗ Server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Server connection failed: {e}")
        return False

def test_ai_summary():
    """Test AI summary generation"""
    print("\n--- Testing AI Summary Generation ---")
    
    test_transcript = """
    John: Good morning everyone, let's start our weekly team meeting.
    Sarah: Thanks <PERSON>. I wanted to discuss the progress on the new feature.
    Mike: Yes, we've completed about 70% of the development work.
    Sarah: That's great! We should be able to finish by Friday.
    John: Perfect. Mike, can you prepare a demo for next week?
    Mike: Absolutely, I'll have it ready by Monday.
    John: Excellent. Any other items to discuss?
    Sarah: Just a reminder that we have the client presentation on Thursday.
    <PERSON>: Right, thanks for the reminder. Let's wrap up here.
    """
    
    try:
        response = requests.post('http://localhost:6000/api/summarize', 
                               json={'transcript': test_transcript},
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ AI Summary generation successful")
                print("  Summary preview:", result['summary'][:100] + "...")
                return True
            else:
                print(f"✗ AI Summary failed: {result.get('error')}")
                return False
        else:
            print(f"✗ AI Summary request failed with status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ AI Summary request error: {e}")
        return False

def test_sentiment_analysis():
    """Test advanced sentiment analysis"""
    print("\n--- Testing Advanced Sentiment Analysis ---")
    
    test_texts = [
        "I'm really excited about this project! It's going to be amazing.",
        "This is terrible. I hate how this is going.",
        "The meeting was okay, nothing special but not bad either."
    ]
    
    for i, text in enumerate(test_texts, 1):
        try:
            response = requests.post('http://localhost:6000/api/sentiment',
                                   json={'text': text, 'window': 5},
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    sentiment = result.get('overall_sentiment', 0)
                    print(f"✓ Sentiment test {i}: {sentiment:.3f}")
                    print(f"  Text: {text[:50]}...")
                else:
                    print(f"✗ Sentiment test {i} failed: {result.get('error')}")
                    return False
            else:
                print(f"✗ Sentiment test {i} request failed: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ Sentiment test {i} error: {e}")
            return False
    
    return True

def test_attention_tracking():
    """Test attention tracking endpoints"""
    print("\n--- Testing Attention Tracking ---")
    
    # Test start attention tracking
    try:
        response = requests.post('http://localhost:6000/api/attention/start',
                               json={'room': 'test_room'},
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ Attention tracking start successful")
            else:
                print(f"✗ Attention tracking start failed: {result.get('error')}")
                return False
        else:
            print(f"✗ Attention tracking start request failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Attention tracking start error: {e}")
        return False
    
    # Test frame analysis with a dummy frame
    try:
        # Create a dummy image frame
        dummy_image = np.zeros((240, 320, 3), dtype=np.uint8)
        cv2.putText(dummy_image, "Test Frame", (50, 120), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Encode to base64
        _, buffer = cv2.imencode('.jpg', dummy_image)
        base64_frame = "data:image/jpeg;base64," + base64.b64encode(buffer).decode('utf-8')
        
        response = requests.post('http://localhost:6000/api/attention/analyze',
                               json={'frame': base64_frame, 'room': 'test_room'},
                               timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                attention_data = result.get('data', {})
                print("✓ Attention frame analysis successful")
                print(f"  Face detected: {attention_data.get('face_detected', False)}")
                print(f"  Attention score: {attention_data.get('attention_score', 0):.3f}")
            else:
                print(f"✗ Attention frame analysis failed: {result.get('error')}")
                return False
        else:
            print(f"✗ Attention frame analysis request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Attention frame analysis error: {e}")
        return False
    
    # Test stop attention tracking
    try:
        response = requests.post('http://localhost:6000/api/attention/stop',
                               json={'room': 'test_room'},
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ Attention tracking stop successful")
                return True
            else:
                print(f"✗ Attention tracking stop failed: {result.get('error')}")
                return False
        else:
            print(f"✗ Attention tracking stop request failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Attention tracking stop error: {e}")
        return False

def main():
    """Run all integration tests"""
    print("=" * 60)
    print("  Adaptive Meeting Twin - Integration Test Suite")
    print("=" * 60)
    
    # Test server status
    if not test_server_status():
        print("\n❌ Server is not running. Please start the backend server first.")
        print("   Run: python working_server.py")
        sys.exit(1)
    
    # Run all tests
    tests = [
        ("AI Summary Generation", test_ai_summary),
        ("Advanced Sentiment Analysis", test_sentiment_analysis),
        ("Attention Tracking", test_attention_tracking)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print(f"  Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The integration is working correctly.")
        print("\nYou can now:")
        print("1. Open frontend/index.html in your browser")
        print("2. Join a room and test the features manually")
        print("3. Try the AI summary, sentiment analysis, and attention tracking")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        print("   Make sure all required packages are installed.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
