body {
  margin: 0;
  font-family: system-ui, sans-serif;
  background: #f9fafb;
  color: #0f172a;
}
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #0ea5e9;
  color: white;
}
h1 { margin: 0; font-size: 18px; }
.badge {
  background: #111827;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
}
.badge.alert {
  background: #dc2626;
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
.stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}
.grid {
  display: grid;
  grid-template-columns: 1.4fr 1fr 1fr;
  gap: 14px;
  padding: 14px;
}
.card {
  background: white;
  padding: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0,0,0,.1);
}
.video-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}
video {
  width: 100%;
  aspect-ratio: 16 / 9;
  background: #111;
  border-radius: 8px;
}
.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 10px 0;
}
button {
  padding: 8px 10px;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
  cursor: pointer;
  background: #f8fafc;
  transition: background-color 0.2s;
}
button:hover { background: #e2e8f0; }
button:disabled {
  background: #f1f5f9;
  color: #94a3b8;
  cursor: not-allowed;
}
button.danger { background: #fca5a5; }
button.danger:hover { background: #f87171; }

input[type="text"] {
  padding: 8px 10px;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
  margin-right: 8px;
  min-width: 200px;
}

input[type="checkbox"] {
  margin-right: 8px;
}
.scrollbox {
  height: 180px;
  overflow: auto;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 6px;
  background: #f1f5f9;
}
textarea {
  width: 100%;
  min-height: 90px;
  margin: 4px 0;
}
.list { padding-left: 18px; }
#engBars .group { margin-bottom: 8px; }
#engBars .bar {
  height: 10px;
  background: #e2e8f0;
  border-radius: 6px;
  position: relative;
}
#engBars .bar span {
  position: absolute;
  left: 0; top: 0; bottom: 0;
  background: #0ea5e9;
  border-radius: 6px;
}
.label { font-size: 12px; margin-top: 2px; }

#connectionStatus {
  margin-left: auto;
  font-size: 12px;
}

#connectionStatus.connected {
  background: #10b981;
}

#connectionStatus.error {
  background: #ef4444;
}

canvas {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.group {
  margin-bottom: 8px;
}

.group .label {
  font-size: 12px;
  margin-bottom: 4px;
  color: #475569;
}

footer {
  text-align: center;
  padding: 10px;
  color: #475569;
}
