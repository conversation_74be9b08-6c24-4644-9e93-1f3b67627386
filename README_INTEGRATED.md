# Adaptive Meeting Twin - Integrated AI System

A comprehensive video conferencing platform with advanced AI features including real-time sentiment analysis, attention tracking, and AI-powered meeting summaries.

## 🚀 Features

### Core Video Conferencing
- **WebRTC Video Calls**: Peer-to-peer video communication
- **Adaptive Quality**: Automatic fallback based on network conditions
- **Real-time Transcription**: Speech-to-text with live transcript
- **Meeting Recording**: Record and download meeting sessions

### AI-Powered Features
- **🤖 AI Meeting Summaries**: Gemini AI generates comprehensive meeting minutes
- **📊 Advanced Sentiment Analysis**: VADER sentiment analysis with smooth interpolation graphs
- **🎯 Real-time Attention Tracking**: Computer vision-based attention monitoring
- **📈 Engagement Analytics**: Track speaking time and participation

## 🛠️ Technology Stack

### Backend
- **Flask + SocketIO**: WebRTC signaling server with real-time communication
- **Google Gemini AI**: Advanced meeting summarization
- **NLTK + VADER**: Sophisticated sentiment analysis
- **OpenCV + MediaPipe**: Computer vision for attention tracking
- **NumPy + SciPy**: Mathematical processing and interpolation

### Frontend
- **Vanilla JavaScript**: Modern ES6+ with WebRTC APIs
- **Canvas API**: Real-time chart rendering for sentiment and attention
- **WebSocket**: Real-time communication with backend
- **MediaRecorder API**: Meeting recording functionality

## 📦 Installation

### Prerequisites
- Python 3.8+
- Modern web browser with WebRTC support
- Webcam and microphone for full functionality

### Quick Start
1. **Clone and navigate to the project directory**
2. **Run the integrated launcher**:
   ```bash
   start_integrated_system.bat
   ```
   This will:
   - Install required Python packages
   - Start the enhanced backend server
   - Open the frontend in your browser

### Manual Installation
```bash
# Install Python dependencies
pip install flask flask-socketio google-generativeai nltk scipy opencv-python mediapipe numpy

# Download NLTK data
python -c "import nltk; nltk.download('vader_lexicon'); nltk.download('punkt')"

# Start the backend server
python working_server.py

# Open frontend/index.html in your browser
```

## 🧪 Testing

Run the integration test suite to verify all features:
```bash
python test_integration.py
```

This will test:
- Server connectivity
- AI summary generation
- Advanced sentiment analysis
- Attention tracking endpoints

## 🎮 Usage Guide

### Starting a Meeting
1. **Launch the system** using `start_integrated_system.bat`
2. **Enter a room name** and click "Join Room"
3. **Grant camera/microphone permissions** when prompted
4. **Enable transcription** to activate AI features

### AI Features

#### 📝 AI Meeting Summaries
1. Enable transcription during the meeting
2. Speak naturally - the system captures everything
3. Click "Generate Summary" for AI-powered meeting minutes
4. Get structured output with:
   - Executive summary
   - Key discussion points
   - Action items with assignments

#### 📊 Sentiment Analysis
- **Automatic**: Analyzes speech sentiment in real-time
- **Advanced VADER**: More accurate than basic keyword matching
- **Smooth Graphs**: Interpolated curves show sentiment trends
- **Color-coded**: Green (positive), yellow (neutral), red (negative)

#### 🎯 Attention Tracking
1. Click "Start Attention Tracking"
2. System analyzes your video feed for:
   - Eye openness and blink patterns
   - Gaze direction and focus
   - Head pose and orientation
3. Real-time attention score (0-100%)
4. Alerts for low attention or drowsiness

### API Endpoints

The backend provides REST APIs for integration:

```
POST /api/summarize
Content-Type: application/json
{
  "transcript": "Meeting transcript text..."
}

POST /api/sentiment
Content-Type: application/json
{
  "text": "Text to analyze...",
  "window": 5
}

POST /api/attention/start
Content-Type: application/json
{
  "room": "room_name"
}

POST /api/attention/analyze
Content-Type: application/json
{
  "frame": "data:image/jpeg;base64,...",
  "room": "room_name"
}
```

## 🔧 Configuration

### AI Configuration
- **Gemini API Key**: Update in `working_server.py` (line 57)
- **Attention Thresholds**: Modify in `attention_service.py`
- **Sentiment Windows**: Adjust in sentiment analysis calls

### Network Settings
- **Server Port**: Default 6000 (change in `working_server.py`)
- **CORS Origins**: Configured for all origins (modify for production)

## 🚨 Troubleshooting

### Common Issues

**Backend won't start**
- Check Python version (3.8+ required)
- Install missing packages: `pip install -r requirements.txt`
- Verify port 6000 is available

**Camera/microphone not working**
- Grant browser permissions
- Check device availability in browser settings
- Try refreshing the page

**AI features not working**
- Verify internet connection (required for Gemini AI)
- Check API key configuration
- Enable transcription first

**Attention tracking fails**
- Ensure good lighting conditions
- Position face clearly in camera view
- Check OpenCV/MediaPipe installation

### Performance Tips
- **Close unnecessary browser tabs** for better performance
- **Use good lighting** for attention tracking accuracy
- **Stable internet connection** for AI features
- **Modern browser** (Chrome/Firefox recommended)

## 📊 System Architecture

```
Frontend (Browser)
├── WebRTC Video/Audio
├── Real-time Transcription
├── Canvas Charts (Sentiment/Attention)
└── WebSocket Communication
    │
    ▼
Backend Server (Python)
├── Flask + SocketIO (WebRTC Signaling)
├── Gemini AI (Meeting Summaries)
├── NLTK + VADER (Sentiment Analysis)
├── OpenCV + MediaPipe (Attention Tracking)
└── REST APIs + WebSocket Events
```

## 🔒 Privacy & Security

- **Local Processing**: Attention tracking runs locally
- **Secure APIs**: HTTPS recommended for production
- **Data Retention**: Transcripts stored temporarily
- **Camera Access**: Only used for attention tracking when enabled

## 🤝 Contributing

This integrated system combines multiple AI technologies for enhanced meeting experiences. Feel free to extend with additional features like:
- Emotion recognition
- Speaker identification
- Meeting insights dashboard
- Integration with calendar systems

## 📄 License

This project integrates multiple open-source technologies. Please review individual component licenses for commercial use.

---

**🎉 Enjoy your AI-enhanced meetings!**
