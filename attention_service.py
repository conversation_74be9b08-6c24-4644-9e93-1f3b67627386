#!/usr/bin/env python3
"""
Web-based Attention Tracking Service
Converts attention.py functionality for web integration
"""

import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

import cv2
import mediapipe as mp
import numpy as np
from collections import deque
import base64
import json
import threading
import time

class AttentionTracker:
    def __init__(self):
        # MediaPipe setup
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Configuration
        self.CLOSE_EYE_THRESHOLD = 0.22
        self.FPS = 30
        
        # Eye landmark indices
        self.LEFT_EYE_INDICES = [33, 160, 158, 133, 153, 144]
        self.RIGHT_EYE_INDICES = [362, 385, 387, 263, 373, 380]
        self.LEFT_IRIS = [468, 469, 470, 471]
        self.RIGHT_IRIS = [473, 474, 475, 476]
        
        # Buffers for smoothing
        self.ear_history = deque(maxlen=5)
        self.att_history = deque(maxlen=10)
        
        # State tracking
        self.eyes_closed_frames = 0
        self.is_tracking = False
        self.face_mesh = None
        
        # 3D model points for head pose estimation
        self.model_points = np.array([
            (0.0, 0.0, 0.0),            # Nose tip
            (-30.0, -125.0, -30.0),     # Chin
            (-60.0, 40.0, -60.0),       # Left eye corner
            (60.0, 40.0, -60.0),        # Right eye corner
            (-40.0, -40.0, -60.0),      # Left mouth corner
            (40.0, -40.0, -60.0)        # Right mouth corner
        ], dtype=np.float64)
        
        self.landmark_ids = [1, 152, 33, 263, 61, 291]
        
    def initialize_face_mesh(self):
        """Initialize MediaPipe face mesh"""
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.6,
            min_tracking_confidence=0.6
        )
        
    def eye_aspect_ratio(self, landmarks, indices, w, h):
        """Calculate eye aspect ratio"""
        pts = [(int(landmarks[i].x * w), int(landmarks[i].y * h)) for i in indices]
        A = np.linalg.norm(np.array(pts[1]) - np.array(pts[5]))
        B = np.linalg.norm(np.array(pts[2]) - np.array(pts[4]))
        C = np.linalg.norm(np.array(pts[0]) - np.array(pts[3]))
        ear = (A + B) / (2.0 * C)
        return ear
    
    def analyze_frame(self, frame):
        """Analyze a single frame for attention metrics"""
        if self.face_mesh is None:
            self.initialize_face_mesh()
            
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(rgb_frame)
        
        attention_data = {
            "attention_score": 0.0,
            "eye_openness": 0.0,
            "gaze_score": 0.0,
            "head_pose_score": 0.0,
            "face_detected": False,
            "drowsy_alert": False,
            "timestamp": time.time()
        }
        
        if results.multi_face_landmarks:
            face_landmarks = results.multi_face_landmarks[0]
            h, w, _ = frame.shape
            attention_data["face_detected"] = True
            
            # Eye openness analysis
            left_ear = self.eye_aspect_ratio(face_landmarks.landmark, self.LEFT_EYE_INDICES, w, h)
            right_ear = self.eye_aspect_ratio(face_landmarks.landmark, self.RIGHT_EYE_INDICES, w, h)
            avg_ear = (left_ear + right_ear) / 2.0
            self.ear_history.append(avg_ear)
            smooth_ear = sum(self.ear_history) / len(self.ear_history)
            
            if smooth_ear < self.CLOSE_EYE_THRESHOLD:
                self.eyes_closed_frames += 1
                eye_openness_score = 0.0
            else:
                self.eyes_closed_frames = 0
                eye_openness_score = min(1.0, (smooth_ear - self.CLOSE_EYE_THRESHOLD) / (0.3 - self.CLOSE_EYE_THRESHOLD))
            
            attention_data["eye_openness"] = eye_openness_score
            
            # Gaze estimation
            l_cx = int(np.mean([face_landmarks.landmark[i].x for i in self.LEFT_IRIS]) * w)
            l_cy = int(np.mean([face_landmarks.landmark[i].y for i in self.LEFT_IRIS]) * h)
            r_cx = int(np.mean([face_landmarks.landmark[i].x for i in self.RIGHT_IRIS]) * w)
            r_cy = int(np.mean([face_landmarks.landmark[i].y for i in self.RIGHT_IRIS]) * h)
            
            eye_center_x = (l_cx + r_cx) / 2
            eye_center_y = (l_cy + r_cy) / 2
            nx, ny = int(face_landmarks.landmark[1].x * w), int(face_landmarks.landmark[1].y * h)
            
            horiz_norm = abs(eye_center_x - nx) / w
            vert_norm = abs(eye_center_y - ny) / h
            
            gaze_score = max(0.0, 1.0 - (horiz_norm * 5))
            head_pose_score = max(0.0, 1.0 - (vert_norm * 5))
            
            attention_data["gaze_score"] = gaze_score
            attention_data["head_pose_score"] = head_pose_score
            
            # Head pose estimation with solvePnP
            try:
                image_points = np.array([
                    (face_landmarks.landmark[1].x * w, face_landmarks.landmark[1].y * h),     # Nose tip
                    (face_landmarks.landmark[152].x * w, face_landmarks.landmark[152].y * h), # Chin
                    (face_landmarks.landmark[33].x * w, face_landmarks.landmark[33].y * h),   # Left eye
                    (face_landmarks.landmark[263].x * w, face_landmarks.landmark[263].y * h), # Right eye
                    (face_landmarks.landmark[61].x * w, face_landmarks.landmark[61].y * h),   # Left mouth
                    (face_landmarks.landmark[291].x * w, face_landmarks.landmark[291].y * h)  # Right mouth
                ], dtype=np.float64)
                
                focal_length = w
                center = (w/2, h/2)
                camera_matrix = np.array([
                    [focal_length, 0, center[0]],
                    [0, focal_length, center[1]],
                    [0, 0, 1]
                ], dtype=np.float64)
                dist_coeffs = np.zeros((4, 1))
                
                _, rvec, tvec = cv2.solvePnP(self.model_points, image_points, camera_matrix, dist_coeffs)
                rmat, _ = cv2.Rodrigues(rvec)
                angles, _, _, _, _, _ = cv2.RQDecomp3x3(rmat)
                pitch, yaw, roll = [a * 180 for a in angles]
                
                # Penalize large deviations
                if abs(yaw) > 20 or abs(pitch) > 20:
                    head_pose_score *= 0.5
                    
                attention_data["head_pose_score"] = head_pose_score
            except:
                pass
            
            # Calculate overall attention score
            attention_score = (0.4 * gaze_score + 0.3 * head_pose_score + 0.3 * eye_openness_score)
            
            # Apply smoothing
            self.att_history.append(attention_score)
            attention_score = sum(self.att_history) / len(self.att_history)
            
            # Micro-sleep detection
            if self.eyes_closed_frames > self.FPS * 2:
                attention_score = 0.0
                attention_data["drowsy_alert"] = True
            
            attention_data["attention_score"] = attention_score
        
        return attention_data
    
    def process_base64_frame(self, base64_frame):
        """Process a base64 encoded frame"""
        try:
            # Decode base64 image
            img_data = base64.b64decode(base64_frame.split(',')[1])
            nparr = np.frombuffer(img_data, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if frame is None:
                return {"error": "Failed to decode frame"}
            
            return self.analyze_frame(frame)
        except Exception as e:
            return {"error": str(e)}
    
    def start_tracking(self):
        """Start attention tracking"""
        self.is_tracking = True
        self.initialize_face_mesh()
        
    def stop_tracking(self):
        """Stop attention tracking"""
        self.is_tracking = False
        if self.face_mesh:
            self.face_mesh.close()
            self.face_mesh = None
        
        # Clear buffers
        self.ear_history.clear()
        self.att_history.clear()
        self.eyes_closed_frames = 0

# Global tracker instance
attention_tracker = AttentionTracker()

def get_attention_tracker():
    """Get the global attention tracker instance"""
    return attention_tracker

if __name__ == "__main__":
    # Test the attention tracker with webcam
    tracker = AttentionTracker()
    tracker.start_tracking()
    
    cap = cv2.VideoCapture(0)
    
    try:
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
                
            frame = cv2.flip(frame, 1)
            attention_data = tracker.analyze_frame(frame)
            
            # Display results
            if attention_data["face_detected"]:
                cv2.putText(frame, f"Attention: {attention_data['attention_score']:.2f}", 
                           (30, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
                
                if attention_data["drowsy_alert"]:
                    cv2.putText(frame, "ALERT: Drowsy!", (30, 120),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
            else:
                cv2.putText(frame, "No face detected", (30, 40),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            cv2.imshow("Attention Tracking Service", frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
    finally:
        cap.release()
        cv2.destroyAllWindows()
        tracker.stop_tracking()
